# 结直肠癌筛查微观模拟模型 产品需求文档 (PRD)

## 目标与背景

### 项目目标

本PRD旨在交付一个全面的结直肠癌筛查微观模拟模型，实现以下目标：

- **支持循证政策制定**：为中国医疗卫生部门提供科学严谨的工具，优化国家结直肠癌筛查策略
- **弥合研究与实践差距**：将复杂的流行病学研究转化为可操作的筛查政策建议
- **确立技术领先地位**：使中国在本土化癌症筛查模拟技术领域处于领先地位
- **支持卫生经济学决策**：基于中国人群数据进行筛查策略的成本效益分析
- **促进个性化筛查**：支持个体化风险评估和筛查建议

### 背景介绍

结直肠癌是全球第二大癌症死因，但通过有效筛查可高度预防。目前中国使用的筛查模型主要改编自西方人群，在中国人口统计学特征、遗传特征和医疗体系的准确性和相关性方面存在关键差距。

本项目解决了对**本土化、科学严谨的微观模拟模型**的迫切需求，该模型能够：
- 准确模拟中国人群的疾病进展
- 同时评估多种筛查策略
- 使用中国成本结构进行卫生经济学分析
- 为政策制定者提供循证筛查建议

该模型在结合**双队列架构**（自然人群队列和出生队列）、**基于机器学习的校准**和**灵活筛查策略模拟**方面实现了突破——这些能力在现有模型中尚不具备。

### 变更日志

| 版本 | 日期 | 变更内容 | 作者 |
|------|------|----------|------|
| 1.0 | 2025-01-31 | 从技术规格书重构为综合PRD文档 | 产品经理 |

## 需求规格

### 功能需求

**FR1**：系统应能模拟动态人群队列，支持可配置的初始人口统计学特征（年龄分布、性别比例、人口规模），并基于中国生命表进行自然死亡率进展模拟。

**FR2**：系统应实现双重疾病进展通路：腺瘤-癌变序列（85%病例）和锯齿状腺瘤-癌变序列（15%病例），具有不同的进展参数。

**FR3**：系统应支持个体风险因素评估，包括家族史、炎性肠病、肥胖、糖尿病、吸烟和久坐生活方式，影响腺瘤产生率。

**FR4**：系统应能模拟多种筛查工具，包括粪便免疫化学检测（FIT）、结肠镜检查、乙状结肠镜检查、风险评估问卷及其他，具有可配置的针对腺瘤和癌症不同阶段的敏感性和特异性参数。

**FR5**：系统应支持灵活的筛查策略，包括可配置的开始/结束年龄、间隔、依从性和阳性后结肠镜筛查依从性，以及贯序工具实施。

**FR6**：系统应能计算卫生经济学结果，包括直接筛查成本、治疗成本、质量调整生命年（QALY）、挽救生命年（LYG）和增量成本效益比（ICER）。

**FR7**：系统应实现基于机器学习的模型校准，使用深度神经网络和拉丁超立方抽样生成10,000个参数组合和95%置信区间。

**FR8**：系统应提供全面的数据输入管理，包括校准基准值、人口结构表、生命表和筛查工具参数。

**FR9**：系统应生成详细的模拟输出，包括年龄性别特异性腺瘤患病率、癌症发病率、死亡率和筛查策略有效性指标。

**FR10**：系统应支持长期模拟周期（最长100年），具有年度进展周期和状态转换。

### 非功能需求

**NFR1**：系统应支持对多达100万个体进行100年周期的模拟，单次模拟运行时间不超过30分钟。

**NFR2**：系统在大规模模拟期间应将内存使用量保持在8GB以下，并支持并行计算加速。

**NFR3**：系统应提供模块化架构，能够轻松添加新的筛查工具、风险因素和进展通路，无需修改核心系统。

**NFR4**：系统应实现全面的参数验证、异常处理和详细日志记录，用于调试和审计目的。

**NFR5**：系统应保持模拟结果的一致性和可重现性，具有自动备份和恢复机制。

**NFR6**：系统应提供直观的用户界面（Web、CLI、API），配有全面的文档、教程和错误消息。

**NFR7**：系统应支持多种数据输入格式（CSV、JSON、Excel），并提供数据导出功能以与外部分析工具集成。

**NFR8**：系统应实现3%年度折现率进行经济计算，并支持关键经济参数的敏感性分析。

## 用户体验设计

### 整体用户体验愿景

结直肠癌筛查微观模拟模型将提供**专业的研究级界面**，平衡复杂的分析功能与直观的可用性。系统将作为医疗政策制定者、研究人员和公共卫生官员的**可信科学工具**。

**核心用户体验原则**：
- **科学可信度**：专业界面建立对结果的信心
- **渐进式披露**：复杂参数按逻辑工作流组织
- **透明度**：清晰展示模型假设和计算过程
- **可重现性**：便于分享和记录模拟配置

### 关键交互模式

**1. 引导式模拟工作流**：分步向导设置复杂模拟，每个阶段都有验证

**2. 参数配置面板**：有组织的参数组，包含上下文帮助、默认值和范围验证

**3. 实时验证**：对参数冲突、缺失数据或无效配置提供即时反馈

**4. 结果仪表板**：交互式可视化，具有下钻功能和导出选项

**5. 场景比较**：多种筛查策略的并排比较，突出显示差异

### 核心界面和视图

**配置仪表板**：设置模拟参数、管理数据输入和访问保存配置的中央枢纽

**人群设置界面**：定义人口统计学特征、风险因素分布和队列特征的界面

**筛查策略设计器**：配置筛查协议、工具序列和依从性参数的可视化工具

**校准界面**：机器学习校准设置，包含参数范围、目标基准和收敛监控

**模拟监控器**：实时进度跟踪，显示预计完成时间和资源使用指标

**结果分析仪表板**：全面的结果可视化，包含图表、表格和统计摘要

**经济分析视图**：专门的卫生经济学结果界面，包含成本效益可视化

**导出和报告中心**：生成报告、导出数据和分享模拟配置的工具

### 无障碍访问：WCAG AA

系统将符合WCAG AA无障碍标准，确保残障研究人员的可用性，包括：
- 所有交互元素的键盘导航支持
- 具有适当ARIA标签的屏幕阅读器兼容性
- 数据可视化的高对比度配色方案
- 所有图表和图形的替代文本

### 品牌设计

**科学研究美学**：简洁、专业的界面，强调数据清晰度和科学严谨性。配色方案应传达信任和准确性，同时保持复杂数据展示的视觉层次。

## 技术方案

### 代码仓库结构：单体仓库

**设计理由**：单一仓库结构便于模拟引擎、校准模块和用户界面的集成开发，同时保持清晰的模块边界。

**结构设计**：
```
colorectal-screening-model/
├── src/
│   ├── core/           # 核心模拟引擎
│   ├── modules/        # 疾病、人群、筛查模块
│   ├── calibration/    # 机器学习校准组件
│   ├── economics/      # 卫生经济学分析
│   └── interfaces/     # Web界面、CLI、API接口
├── data/              # 输入数据、基准值、生命表
├── tests/             # 综合测试套件
├── docs/              # 技术和用户文档
└── examples/          # 示例配置和教程
```

### 服务架构：模块化单体

**核心模拟引擎**：管理人群队列、疾病进展和筛查干预的中央协调器

**可插拔模块系统**：疾病模块、筛查工具和经济评估器的标准化接口

**校准服务**：基于机器学习的参数优化独立服务，支持GPU加速

**数据管理层**：支持多种输入格式和验证的统一数据访问层

### 编程语言和框架

**主要语言**：Python 3.8+，兼容科学计算生态系统并提供广泛的库支持

**核心依赖**：
- **NumPy/SciPy**：数值计算和统计函数
- **Pandas**：数据操作和分析
- **Scikit-learn**：校准用机器学习算法
- **TensorFlow/PyTorch**：高级校准用深度神经网络
- **Matplotlib/Plotly**：数据可视化和结果展示

**Web界面**：Flask/FastAPI用于REST API和Web仪表板

**数据库**：开发环境使用SQLite，生产环境使用PostgreSQL

### 测试策略：完整测试金字塔

**单元测试**：个别模块和函数的全面覆盖
**集成测试**：模块交互和数据流验证
**系统测试**：端到端模拟准确性和性能测试
**验证测试**：与已发表流行病学数据的比较

### 部署和运维

**容器化**：Docker容器确保跨环境的一致部署
**编排**：本地开发使用Docker Compose，生产扩展使用Kubernetes
**CI/CD流水线**：使用GitHub Actions进行自动化测试、验证和部署
**监控**：应用性能监控和模拟结果验证

## 史诗概览

### 史诗1：基础设施和核心模拟引擎

建立项目基础设施并实现能够管理人群队列和基本疾病进展的核心微观模拟引擎。此史诗交付具有基本功能的工作模拟框架，同时建立所有开发、测试和部署基础设施。

**关键交付物**：项目设置、核心数据结构、基本人群模拟、CI/CD流水线、初始Web界面

### 史诗2：疾病进展建模系统

实现全面的疾病自然史建模，包括腺瘤-癌变和锯齿状腺瘤通路，具有可配置的进展参数。此史诗交付具有准确疾病建模的模拟科学核心。

**关键交付物**：双通路疾病进展、风险因素集成、进展参数管理、疾病状态转换

### 史诗3：筛查策略模拟引擎

开发支持多种筛查工具、灵活策略和依从性建模的筛查模块。此史诗实现比较不同筛查方法的主要用例。

**关键交付物**：多工具筛查支持、策略配置、依从性建模、筛查结果计算

### 史诗4：卫生经济学分析模块

实现全面的卫生经济学评估，包括成本计算、QALY/LYG指标和成本效益分析。此史诗提供政策决策必需的经济分析能力。

**关键交付物**：成本建模、结果指标计算、经济分析仪表板、敏感性分析工具

### 史诗5：机器学习校准系统

开发使用深度神经网络和拉丁超立方抽样进行自动参数优化的高级校准系统。此史诗交付区别于现有解决方案的技术突破。

**关键交付物**：机器学习校准引擎、参数优化、置信区间计算、校准验证工具

### 史诗6：高级分析和报告

创建全面的结果分析、可视化和报告功能，包括场景比较和导出功能。此史诗通过专业级分析工具完善用户体验。

**关键交付物**：结果仪表板、比较分析、数据导出、报告生成、可视化工具

## Epic 1: Foundation Infrastructure and Core Simulation Engine

**Epic Goal**: Establish a robust, scalable foundation for the colorectal cancer screening microsimulation model with core population management capabilities and essential development infrastructure. This epic delivers a working simulation framework that can manage population cohorts and provides the foundation for all subsequent development.

### Story 1.1: Project Infrastructure Setup

As a **developer**,
I want **a complete project structure with development environment configuration**,
so that **the team can begin development with consistent tooling and deployment capabilities**.

#### Acceptance Criteria

1. Python 3.8+ project structure created with virtual environment configuration
2. Core dependencies installed (NumPy, SciPy, Pandas, pytest)
3. Git repository initialized with appropriate .gitignore and README
4. Docker configuration created for containerized development
5. Basic CI/CD pipeline configured with GitHub Actions
6. Code quality tools configured (linting, formatting, type checking)

### Story 1.2: Core Data Structures Implementation

As a **simulation engine**,
I want **fundamental data structures for individuals, populations, and simulation state**,
so that **I can represent and manage population cohorts throughout the simulation**.

#### Acceptance Criteria

1. Individual dataclass implemented with demographics, health state, and history tracking
2. Population container class created with efficient individual management
3. Simulation state management system implemented
4. Basic data validation and error handling added
5. Unit tests created for all core data structures
6. Documentation added for data structure usage

### Story 1.3: Basic Population Initialization

As a **researcher**,
I want **to initialize population cohorts with configurable demographics**,
so that **I can set up simulation scenarios with realistic population characteristics**.

#### Acceptance Criteria

1. Population initialization function accepts age distribution, gender ratio, and size parameters
2. Individual demographics generated according to specified distributions
3. Age and gender validation implemented with appropriate constraints
4. Population statistics calculation functions implemented
5. Configuration file support added for population parameters
6. Basic population summary reporting implemented

### Story 1.4: Life Table Integration and Mortality Modeling

As a **simulation engine**,
I want **to apply natural mortality based on Chinese life tables**,
so that **population aging and natural death are accurately modeled**.

#### Acceptance Criteria

1. Life table data loading and validation system implemented
2. Age and gender-specific mortality rate calculation functions created
3. Annual mortality application with random sampling implemented
4. Population survival statistics tracking added
5. Life table data format documentation created
6. Mortality calculation unit tests implemented

### Story 1.5: Basic Web Interface Framework

As a **user**,
I want **a simple web interface to configure and run basic simulations**,
so that **I can interact with the simulation system without command-line tools**.

#### Acceptance Criteria

1. Flask/FastAPI web framework setup with basic routing
2. Simple HTML interface for population configuration created
3. API endpoints for simulation initialization and status implemented
4. Basic form validation and error handling added
5. Simple results display page implemented
6. Web interface accessible via Docker container

## Epic 2: Disease Progression Modeling System

**Epic Goal**: Implement scientifically accurate disease natural history modeling with dual progression pathways (adenoma-carcinoma and serrated adenoma pathways) and individual risk factor integration. This epic delivers the core scientific functionality that enables accurate simulation of colorectal cancer development and progression.

### Story 2.1: Risk Factor Management System

As a **researcher**,
I want **to define and manage individual risk factors with configurable weights**,
so that **I can model personalized disease risk based on established epidemiological factors**.

#### Acceptance Criteria

1. Risk factor enumeration system implemented (family history, IBD, obesity, diabetes, smoking, sedentary lifestyle)
2. Individual risk factor assignment with boolean and continuous value support
3. Risk factor weight configuration system with literature-based defaults
4. Composite risk score calculation function implemented
5. Risk factor validation and constraint checking added
6. Risk factor impact documentation and references included

### Story 2.2: Adenoma-Carcinoma Pathway Implementation

As a **simulation engine**,
I want **to model the adenoma-carcinoma progression pathway with configurable parameters**,
so that **I can accurately simulate the primary route of colorectal cancer development (85% of cases)**.

#### Acceptance Criteria

1. Disease state enumeration implemented (Normal → Low-risk adenoma → High-risk adenoma → Preclinical cancer → Clinical cancer)
2. Sigmoid function adenoma generation based on age, gender, and risk factors
3. Normal distribution progression modeling for adenoma advancement
4. Sojourn time modeling for each disease state
5. Gender-specific progression multipliers implemented
6. Anatomical location assignment (proximal colon, distal colon, rectum)

### Story 2.3: Serrated Adenoma Pathway Implementation

As a **simulation engine**,
I want **to model the serrated adenoma-carcinoma progression pathway**,
so that **I can simulate the alternative route of colorectal cancer development (15% of cases)**.

#### Acceptance Criteria

1. Serrated adenoma disease states implemented (Normal → Small serrated → Large serrated → Preclinical → Clinical)
2. Serrated adenoma probability assignment (15% of total adenoma cases)
3. Distinct progression parameters for serrated pathway
4. Integration with main disease progression engine
5. Serrated adenoma-specific progression rates implemented
6. Pathway selection logic with proper probability distribution

## Next Steps

### UX Expert Prompt

"Please create a comprehensive front-end specification for the colorectal cancer screening microsimulation model based on this PRD. Focus on the scientific research user experience with emphasis on parameter configuration workflows, results visualization, and professional data presentation. The interface should serve healthcare policy researchers and public health officials who need to configure complex simulations and analyze results with confidence."

### Architect Prompt

"Please create a detailed technical architecture document for the colorectal cancer screening microsimulation model based on this PRD. Focus on the modular simulation engine design, machine learning calibration system integration, performance optimization for large-scale population simulations, and scalable deployment architecture. Ensure the architecture supports the dual-pathway disease modeling and flexible screening strategy simulation requirements."
