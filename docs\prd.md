# 结直肠癌筛查微观模拟模型 Product Requirements Document (PRD)

## Goals and Background Context

### Goals

This PRD aims to deliver a comprehensive colorectal cancer screening microsimulation model that will:

- **Enable Evidence-Based Policy Making**: Provide Chinese healthcare authorities with scientifically rigorous tools to optimize national colorectal cancer screening strategies
- **Bridge Research-to-Practice Gap**: Transform complex epidemiological research into actionable screening policy recommendations
- **Establish Technical Leadership**: Position China as a leader in localized cancer screening simulation technology
- **Support Health Economic Decision Making**: Enable cost-effectiveness analysis of screening strategies using Chinese population data
- **Facilitate Personalized Screening**: Support individualized risk assessment and screening recommendations

### Background Context

Colorectal cancer is the second leading cause of cancer death globally, yet highly preventable through effective screening. Current screening models used in China are predominantly adapted from Western populations, creating a critical gap in accuracy and relevance for Chinese demographics, genetic profiles, and healthcare systems.

This project addresses the urgent need for a **localized, scientifically rigorous microsimulation model** that can:
- Accurately model disease progression in Chinese populations
- Evaluate multiple screening strategies simultaneously
- Provide health economic analysis using Chinese cost structures
- Support policy makers with evidence-based screening recommendations

The model represents a breakthrough in combining **dual-cohort architecture** (natural population and birth cohorts), **machine learning-based calibration**, and **flexible screening strategy simulation** - capabilities not available in existing models.

### Change Log

| Version | Date | Changes | Author |
|---------|------|---------|---------|
| 1.0 | 2025-01-31 | Initial comprehensive PRD restructure from technical specification | Product Manager |

## Requirements

### Functional Requirements

**FR1**: The system shall simulate dynamic population cohorts with configurable initial demographics (age distribution, gender ratio, population size) and natural mortality progression based on Chinese life tables.

**FR2**: The system shall implement dual disease progression pathways: adenoma-carcinoma sequence (85% of cases) and serrated adenoma-carcinoma sequence (15% of cases) with distinct progression parameters.

**FR3**: The system shall support individual risk factor assessment including family history, inflammatory bowel disease, obesity, diabetes, smoking, and sedentary lifestyle, affecting adenoma generation rates.

**FR4**: The system shall simulate multiple screening tools including FIT (Fecal Immunochemical Test), colonoscopy, sigmoidoscopy, and risk assessment questionnaires with configurable sensitivity and specificity parameters.

**FR5**: The system shall support flexible screening strategies with configurable start/end ages, intervals, sequential tool implementation, and compliance rates.

**FR6**: The system shall calculate health economic outcomes including direct screening costs, treatment costs, Quality-Adjusted Life Years (QALY), Life Years Gained (LYG), and Incremental Cost-Effectiveness Ratios (ICER).

**FR7**: The system shall implement machine learning-based model calibration using deep neural networks with Latin Hypercube Sampling to generate 10,000 parameter combinations and 95% confidence intervals.

**FR8**: The system shall provide comprehensive data input management for calibration benchmarks, population structure tables, life tables, and screening tool parameters.

**FR9**: The system shall generate detailed simulation outputs including age-gender specific adenoma prevalence, cancer incidence rates, mortality rates, and screening strategy effectiveness metrics.

**FR10**: The system shall support long-term simulation periods (up to 100 years) with annual progression cycles and state transitions.

### Non-Functional Requirements

**NFR1**: The system shall support simulation of up to 1 million individuals over 100-year periods with execution time not exceeding 30 minutes per simulation run.

**NFR2**: The system shall maintain memory usage below 8GB during large-scale simulations and support parallel computing acceleration.

**NFR3**: The system shall provide modular architecture enabling easy addition of new screening tools, risk factors, and progression pathways without core system modifications.

**NFR4**: The system shall implement comprehensive parameter validation, exception handling, and detailed logging for debugging and audit purposes.

**NFR5**: The system shall maintain simulation result consistency and reproducibility with automated backup and recovery mechanisms.

**NFR6**: The system shall provide intuitive user interfaces (web, CLI, API) with comprehensive documentation, tutorials, and error messaging.

**NFR7**: The system shall support multiple data input formats (CSV, JSON, Excel) and provide data export capabilities for integration with external analysis tools.

**NFR8**: The system shall implement 3% annual discount rate for economic calculations and support sensitivity analysis for key economic parameters.

## User Experience

### Overall UX Vision

The colorectal cancer screening microsimulation model will provide a **professional, research-grade interface** that balances sophisticated analytical capabilities with intuitive usability. The system will serve as a **trusted scientific instrument** for healthcare policy makers, researchers, and public health officials.

**Key UX Principles**:
- **Scientific Credibility**: Professional interface that instills confidence in results
- **Progressive Disclosure**: Complex parameters organized in logical workflows
- **Transparency**: Clear visibility into model assumptions and calculations
- **Reproducibility**: Easy sharing and documentation of simulation configurations

### Key Interaction Paradigms

**1. Guided Simulation Workflow**: Step-by-step wizard for setting up complex simulations with validation at each stage

**2. Parameter Configuration Panels**: Organized parameter groups with contextual help, default values, and range validation

**3. Real-time Validation**: Immediate feedback on parameter conflicts, missing data, or invalid configurations

**4. Results Dashboard**: Interactive visualizations with drill-down capabilities and export options

**5. Scenario Comparison**: Side-by-side comparison of multiple screening strategies with difference highlighting

### Core Screens and Views

**Configuration Dashboard**: Central hub for setting up simulation parameters, managing data inputs, and accessing saved configurations

**Population Setup Screen**: Interface for defining population demographics, risk factor distributions, and cohort characteristics

**Screening Strategy Designer**: Visual tool for configuring screening protocols, tool sequences, and compliance parameters

**Calibration Interface**: Machine learning calibration setup with parameter ranges, target benchmarks, and convergence monitoring

**Simulation Monitor**: Real-time progress tracking with estimated completion times and resource usage indicators

**Results Analysis Dashboard**: Comprehensive results visualization with charts, tables, and statistical summaries

**Economic Analysis View**: Specialized interface for health economic outcomes with cost-effectiveness visualizations

**Export and Reporting Center**: Tools for generating reports, exporting data, and sharing simulation configurations

### Accessibility: WCAG AA

The system will comply with WCAG AA accessibility standards to ensure usability by researchers with disabilities, including:
- Keyboard navigation support for all interactive elements
- Screen reader compatibility with proper ARIA labels
- High contrast color schemes for data visualizations
- Alternative text for all charts and graphs

### Branding

**Scientific Research Aesthetic**: Clean, professional interface with emphasis on data clarity and scientific rigor. Color palette should convey trust and accuracy while maintaining visual hierarchy for complex data presentations.

## Technical Approach

### Repository Structure: Monorepo

**Rationale**: Single repository structure facilitates integrated development of simulation engine, calibration modules, and user interfaces while maintaining clear module boundaries.

**Structure**:
```
colorectal-screening-model/
├── src/
│   ├── core/           # Core simulation engine
│   ├── modules/        # Disease, population, screening modules  
│   ├── calibration/    # ML calibration components
│   ├── economics/      # Health economic analysis
│   └── interfaces/     # Web UI, CLI, API interfaces
├── data/              # Input data, benchmarks, life tables
├── tests/             # Comprehensive test suite
├── docs/              # Technical and user documentation
└── examples/          # Sample configurations and tutorials
```

### Service Architecture: Modular Monolith

**Core Simulation Engine**: Central orchestrator managing population cohorts, disease progression, and screening interventions

**Pluggable Module System**: Standardized interfaces for disease modules, screening tools, and economic evaluators

**Calibration Service**: Separate service for machine learning-based parameter optimization with GPU acceleration support

**Data Management Layer**: Unified data access layer supporting multiple input formats and validation

### Programming Language and Framework

**Primary Language**: Python 3.8+ for scientific computing ecosystem compatibility and extensive library support

**Core Dependencies**:
- **NumPy/SciPy**: Numerical computing and statistical functions
- **Pandas**: Data manipulation and analysis
- **Scikit-learn**: Machine learning algorithms for calibration
- **TensorFlow/PyTorch**: Deep neural networks for advanced calibration
- **Matplotlib/Plotly**: Data visualization and results presentation

**Web Interface**: Flask/FastAPI for REST API and web dashboard

**Database**: SQLite for development, PostgreSQL for production deployments

### Testing Strategy: Full Testing Pyramid

**Unit Tests**: Comprehensive coverage of individual modules and functions
**Integration Tests**: Module interaction and data flow validation  
**System Tests**: End-to-end simulation accuracy and performance
**Validation Tests**: Comparison against published epidemiological data

### Deployment and Operations

**Containerization**: Docker containers for consistent deployment across environments
**Orchestration**: Docker Compose for local development, Kubernetes for production scaling
**CI/CD Pipeline**: Automated testing, validation, and deployment using GitHub Actions
**Monitoring**: Application performance monitoring and simulation result validation

## Epic Overview

### Epic 1: Foundation Infrastructure and Core Simulation Engine

Establish the foundational project infrastructure and implement the core microsimulation engine capable of managing population cohorts and basic disease progression. This epic delivers a working simulation framework with basic functionality while setting up all development, testing, and deployment infrastructure.

**Key Deliverables**: Project setup, core data structures, basic population simulation, CI/CD pipeline, initial web interface

### Epic 2: Disease Progression Modeling System

Implement comprehensive disease natural history modeling including both adenoma-carcinoma and serrated adenoma pathways with configurable progression parameters. This epic delivers the scientific core of the simulation with accurate disease modeling.

**Key Deliverables**: Dual pathway disease progression, risk factor integration, progression parameter management, disease state transitions

### Epic 3: Screening Strategy Simulation Engine

Develop the screening module supporting multiple screening tools, flexible strategies, and compliance modeling. This epic enables the primary use case of comparing different screening approaches.

**Key Deliverables**: Multi-tool screening support, strategy configuration, compliance modeling, screening outcome calculation

### Epic 4: Health Economic Analysis Module

Implement comprehensive health economic evaluation including cost calculations, QALY/LYG metrics, and cost-effectiveness analysis. This epic provides the economic analysis capabilities essential for policy decision-making.

**Key Deliverables**: Cost modeling, outcome metrics calculation, economic analysis dashboard, sensitivity analysis tools

### Epic 5: Machine Learning Calibration System

Develop the advanced calibration system using deep neural networks and Latin Hypercube Sampling for automated parameter optimization. This epic delivers the technical breakthrough that differentiates this model from existing solutions.

**Key Deliverables**: ML calibration engine, parameter optimization, confidence interval calculation, calibration validation tools

### Epic 6: Advanced Analytics and Reporting

Create comprehensive results analysis, visualization, and reporting capabilities including scenario comparison and export functionality. This epic completes the user experience with professional-grade analysis tools.

**Key Deliverables**: Results dashboard, comparative analysis, data export, report generation, visualization tools

## Epic 1: Foundation Infrastructure and Core Simulation Engine

**Epic Goal**: Establish a robust, scalable foundation for the colorectal cancer screening microsimulation model with core population management capabilities and essential development infrastructure. This epic delivers a working simulation framework that can manage population cohorts and provides the foundation for all subsequent development.

### Story 1.1: Project Infrastructure Setup

As a **developer**,
I want **a complete project structure with development environment configuration**,
so that **the team can begin development with consistent tooling and deployment capabilities**.

#### Acceptance Criteria

1. Python 3.8+ project structure created with virtual environment configuration
2. Core dependencies installed (NumPy, SciPy, Pandas, pytest)
3. Git repository initialized with appropriate .gitignore and README
4. Docker configuration created for containerized development
5. Basic CI/CD pipeline configured with GitHub Actions
6. Code quality tools configured (linting, formatting, type checking)

### Story 1.2: Core Data Structures Implementation

As a **simulation engine**,
I want **fundamental data structures for individuals, populations, and simulation state**,
so that **I can represent and manage population cohorts throughout the simulation**.

#### Acceptance Criteria

1. Individual dataclass implemented with demographics, health state, and history tracking
2. Population container class created with efficient individual management
3. Simulation state management system implemented
4. Basic data validation and error handling added
5. Unit tests created for all core data structures
6. Documentation added for data structure usage

### Story 1.3: Basic Population Initialization

As a **researcher**,
I want **to initialize population cohorts with configurable demographics**,
so that **I can set up simulation scenarios with realistic population characteristics**.

#### Acceptance Criteria

1. Population initialization function accepts age distribution, gender ratio, and size parameters
2. Individual demographics generated according to specified distributions
3. Age and gender validation implemented with appropriate constraints
4. Population statistics calculation functions implemented
5. Configuration file support added for population parameters
6. Basic population summary reporting implemented

### Story 1.4: Life Table Integration and Mortality Modeling

As a **simulation engine**,
I want **to apply natural mortality based on Chinese life tables**,
so that **population aging and natural death are accurately modeled**.

#### Acceptance Criteria

1. Life table data loading and validation system implemented
2. Age and gender-specific mortality rate calculation functions created
3. Annual mortality application with random sampling implemented
4. Population survival statistics tracking added
5. Life table data format documentation created
6. Mortality calculation unit tests implemented

### Story 1.5: Basic Web Interface Framework

As a **user**,
I want **a simple web interface to configure and run basic simulations**,
so that **I can interact with the simulation system without command-line tools**.

#### Acceptance Criteria

1. Flask/FastAPI web framework setup with basic routing
2. Simple HTML interface for population configuration created
3. API endpoints for simulation initialization and status implemented
4. Basic form validation and error handling added
5. Simple results display page implemented
6. Web interface accessible via Docker container

## Epic 2: Disease Progression Modeling System

**Epic Goal**: Implement scientifically accurate disease natural history modeling with dual progression pathways (adenoma-carcinoma and serrated adenoma pathways) and individual risk factor integration. This epic delivers the core scientific functionality that enables accurate simulation of colorectal cancer development and progression.

### Story 2.1: Risk Factor Management System

As a **researcher**,
I want **to define and manage individual risk factors with configurable weights**,
so that **I can model personalized disease risk based on established epidemiological factors**.

#### Acceptance Criteria

1. Risk factor enumeration system implemented (family history, IBD, obesity, diabetes, smoking, sedentary lifestyle)
2. Individual risk factor assignment with boolean and continuous value support
3. Risk factor weight configuration system with literature-based defaults
4. Composite risk score calculation function implemented
5. Risk factor validation and constraint checking added
6. Risk factor impact documentation and references included

### Story 2.2: Adenoma-Carcinoma Pathway Implementation

As a **simulation engine**,
I want **to model the adenoma-carcinoma progression pathway with configurable parameters**,
so that **I can accurately simulate the primary route of colorectal cancer development (85% of cases)**.

#### Acceptance Criteria

1. Disease state enumeration implemented (Normal → Low-risk adenoma → High-risk adenoma → Preclinical cancer → Clinical cancer)
2. Sigmoid function adenoma generation based on age, gender, and risk factors
3. Normal distribution progression modeling for adenoma advancement
4. Sojourn time modeling for each disease state
5. Gender-specific progression multipliers implemented
6. Anatomical location assignment (proximal colon, distal colon, rectum)

### Story 2.3: Serrated Adenoma Pathway Implementation

As a **simulation engine**,
I want **to model the serrated adenoma-carcinoma progression pathway**,
so that **I can simulate the alternative route of colorectal cancer development (15% of cases)**.

#### Acceptance Criteria

1. Serrated adenoma disease states implemented (Normal → Small serrated → Large serrated → Preclinical → Clinical)
2. Serrated adenoma probability assignment (15% of total adenoma cases)
3. Distinct progression parameters for serrated pathway
4. Integration with main disease progression engine
5. Serrated adenoma-specific progression rates implemented
6. Pathway selection logic with proper probability distribution

## Next Steps

### UX Expert Prompt

"Please create a comprehensive front-end specification for the colorectal cancer screening microsimulation model based on this PRD. Focus on the scientific research user experience with emphasis on parameter configuration workflows, results visualization, and professional data presentation. The interface should serve healthcare policy researchers and public health officials who need to configure complex simulations and analyze results with confidence."

### Architect Prompt

"Please create a detailed technical architecture document for the colorectal cancer screening microsimulation model based on this PRD. Focus on the modular simulation engine design, machine learning calibration system integration, performance optimization for large-scale population simulations, and scalable deployment architecture. Ensure the architecture supports the dual-pathway disease modeling and flexible screening strategy simulation requirements."
