# 结直肠癌筛查微观模拟模型 - 产品需求文档 (PRD)

## 1. 项目概述

### 1.1 项目背景

构建国内首个基于多源异构数据融合的结直肠癌筛查微观模拟模型，突破现有模型本土化不足的技术瓶颈，为优化我国结直肠癌筛查策略提供科学工具。

### 1.2 项目目标

- 支持自然人群队列和出生队列动态模拟的双重架构
- 集成个体疾病风险模块
- 兼容多种主流筛查工具及其筛查策略
- 预测筛查策略对结直肠癌发病、死亡的长期影响效果
- 内嵌筛查策略卫生经济学评价模块（QALY、LYG、ICER）

### 1.3 技术突破

- 首创支持非固定筛查周期（可以指定筛查年数）、两个筛查工具组合贯序实施的模拟引擎
- 实现基于机器学习的自适应校准功能，建立疾病自然史动态调参机制

## 2. 功能需求

### 2.1 人口模块

**功能描述**: 模拟动态衰减人口，不考虑人口迁徙及人口出生

**核心功能**:

- 人口初始化：根据年龄分布、性别比例和人口数生成初始人群
- 人口更新：基于生命表计算自然死亡率，更新人口状态
- 人口统计：实时统计人口规模、年龄结构、性别分布

**参数要求**:

- 人口数、性别、年龄、存活状态
- 性别比例设定
- 人群年龄构成比
- 最大年龄限制（100岁）
- 人口寿命表数据源

### 2.2 疾病风险模块

**功能描述**: 基于文献综述确定结直肠癌疾病风险因素及其风险值

**核心功能**:

- 风险因素管理：维护风险因素库及其权重
- 个体风险评估：计算个体综合风险指数
- 风险影响建模：风险因素仅影响腺瘤产生，不影响进展

**风险因素**:

- 结直肠癌一级亲属史
- 炎性肠病史
- 超重肥胖
- 糖尿病
- 吸烟
- 静坐生活方式

### 2.3 疾病自然史模块

**功能描述**: 实现腺瘤-癌通路和锯齿状腺瘤-癌变通路

#### 2.3.1 腺瘤-癌通路（85%）

**疾病阶段**:

- 正常状态
- 低风险腺瘤（≤9mm，且无绒毛无高级别上皮内瘤变）
- 高风险腺瘤（≥10mm或含绒毛或高级别上皮内瘤变）
- 临床前癌症（无症状）
- 临床癌症（I-IV期）

**进展机制**:

- 腺瘤产生：由年龄、性别、个体风险决定（乙状函数分布）
- 腺瘤进展：低风险→高风险→临床前→临床（正态函数分布）
- 癌症结局：生存时长取决于诊断时年龄、癌症位置和分期

#### 2.3.2 锯齿状病变-癌症通路（15%）

**疾病阶段**:

- 正常状态
- 小无蒂锯齿状腺瘤
- 大无蒂锯齿状腺瘤
- 临床前癌症（无症状）
- 临床癌症（I-IV期）

### 2.4 筛查模块

**功能描述**: 支持多种筛查工具和策略的组合实施

**筛查工具**:

- 粪便免疫化学检测（FIT）
- 结肠镜检查
- 乙状结直肠镜检查
- 风险评估问卷
- 其他筛查工具

**筛查参数**:

- 各工具针对不同疾病阶段的敏感性和特异性
- 筛查依从性
- 阳性后接受肠镜检查的依从性

**筛查策略**:

- 筛查起止年龄
- 筛查间隔
- 筛查实施周期
- 筛查工具是否贯序实施（后一个筛查起始年龄不能早于前一筛查结束年龄）

### 2.5 卫生经济学评价模块

**功能描述**: 评估筛查策略的成本效益

**成本计算**:

- 各类筛查成本
- 临床治疗直接成本
- 3%年度折现率

**效益评估**:

- 挽救生命年计算（LYG）
- 质量调整生命年（QALY）
- 成本效益比分析

### 2.6 机器学习校准模块

**功能描述**: 基于深度神经网络的自动校准功能

**核心功能**:

- 拉丁超立方抽样：生成10000个参数组合
- 深度神经网络训练：快速校准模型参数
- 置信区间计算：95%CI置信区间涵盖基准值
- 校准结果与基准值比较统计图（不同年龄校准值点线及其置信区间，与不同年龄基准值点线）

**输入参数**:

- 年龄别腺瘤产生概率（3个系数）
- 年龄别低风险期腺瘤进展概率（3个系数）
- 年龄别高风险期腺瘤进展概率（3个系数）

   #腺瘤产生参数（乙状函数）
    adenoma_generation_a: float=0.1      # 乙状函数参数a
    adenoma_generation_b: float=50.0     # 乙状函数参数b（中点年龄）
    adenoma_generation_c: float=0.01     # 乙状函数参数c（最大概率）

    # 腺瘤进展参数（正态分布）
    low_to_high_risk_mean: float=60.0    # 低风险到高风险进展均值年龄
    low_to_high_risk_std: float=15.0     # 低风险到高风险进展标准差
    high_to_preclinical_mean: float=65.0# 高风险到临床前均值年龄
    high_to_preclinical_std: float=12.0  # 高风险到临床前标准差

    # 癌症进展参数
    preclinical_to_clinical_prob: float=0.1  # 临床前到临床概率
    clinical_progression_prob: float=0.2     # 临床癌症分期进展概率

    # 停留时间参数
    sojourn_time_mean: float=3.0         # 腺瘤停留时间均值（年）
    sojourn_time_std: float=0.5          # 腺瘤停留时间标准差
    dwell_time_mean: float=13.0           # 临床前癌症停留时间均值（年）
    dwell_time_std: float=0.3            # 临床前癌症停留时间标准差

    # 性别效应
    male_adenoma_multiplier: float=1.2   # 男性腺瘤产生倍数
    male_progression_multiplier: float=1.1  # 男性进展倍数

    # 位置分布概率
    proximal_colon_prob: float=0.4       # 近端结肠概率
    distal_colon_prob: float=0.4         # 远端结肠概率
    rectum_prob: float=0.2               # 直肠概率

    # 锯齿状腺瘤参数
    serrated_adenoma_prob: float=0.15    # 锯齿状腺瘤概率
    small_to_large_serrated_prob: float=0.1  # 小到大锯齿状腺瘤进展概率

    # 生存参数
    stage_i_5year_survival: float=0.90   # I期5年生存率
    stage_ii_5year_survival: float=0.80  # II期5年生存率
    stage_iii_5year_survival: float=0.65# III期5年生存率
    stage_iv_5year_survival: float=0.15  # IV期5年生存率

**输出参数**:

- 性别年龄别低风险期腺瘤患病率
- 性别年龄别高风险期腺瘤患病率
- 性别年龄别癌症发病率
- 性别年龄别癌症死亡率
- 性别年龄别分布在直肠的癌症占比

### 2.7 统一数据输入

功能描述：统一的数据初始化入口

* 各种校准基准值
* 人口结构表
* 寿命表
* 筛查工具参数值及筛查策略配置

## 3. 技术架构

### 3.1 系统架构

```
┌─────────────────────────────────────────────────────────┐
│                    用户界面层                            │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐      │
│  │  Web界面    │  │  命令行界面  │  │  API接口    │      │
│  └─────────────┘  └─────────────┘  └─────────────┘      │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                    业务逻辑层                            │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐      │
│  │  模拟引擎    │  │  校准模块    │  │  分析模块    │      │
│  └─────────────┘  └─────────────┘  └─────────────┘      │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                    核心模块层                            │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐      │
│  │  人口模块    │  │  疾病模块    │  │  筛查模块    │      │
│  └─────────────┘  └─────────────┘  └─────────────┘      │
│  ┌─────────────┐  ┌─────────────┐                       │
│  │  风险模块    │  │  经济模块    │                       │
│  └─────────────┘  └─────────────┘                       │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                    数据存储层                            │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐      │
│  │  参数配置    │  │  模拟结果    │  │  校准数据    │      │
│  └─────────────┘  └─────────────┘  └─────────────┘      │
└─────────────────────────────────────────────────────────┘
```

### 3.2 技术栈

- **编程语言**: Python 3.8+
- **科学计算**: NumPy, SciPy, Pandas
- **机器学习**: Scikit-learn, TensorFlow/PyTorch
- **数据可视化**: Matplotlib, Seaborn, Plotly
- **数据存储**: SQLite/PostgreSQL, JSON, CSV
- **Web框架**: Flask/FastAPI (可选)
- **测试框架**: pytest, unittest

### 3.3 数据结构

#### 3.3.1 个体数据结构

```python
@dataclass
class Individual:
    id: int                          # 个体ID
    gender: Gender                   # 性别
    birth_year: int                  # 出生年份
    current_age: int                 # 当前年龄
    alive: bool                      # 存活状态
    cancer_stage: CancerStage        # 癌症阶段
    adenoma_locations: List[str]     # 腺瘤位置
    risk_factors: Dict[str, bool]    # 风险因素
    risk_score: float                # 综合风险评分
    screening_history: List[Dict]    # 筛查历史
    treatment_history: List[Dict]    # 治疗历史
    death_cause: str                 # 死亡原因
```

#### 3.3.2 筛查策略数据结构

```python
@dataclass
class ScreeningStrategy:
    name: str                        # 策略名称
    start_age: int                   # 开始年龄
    end_age: int                     # 结束年龄
    tools: List[ScreeningTool]       # 筛查工具序列
    intervals: List[int]             # 筛查间隔
    sequential: bool                 # 是否贯序实施
    compliance_rate: float           # 依从性参数
    follow_up_compliance_rate: float  # 诊断性肠镜依从性参数
```

## 4. 非功能需求

### 4.1 性能要求

- 支持100万人群规模的长期模拟（100年）
- 单次模拟运行时间不超过30分钟
- 内存使用不超过8GB
- 支持并行计算加速

### 4.2 可扩展性

- 模块化设计，便于添加新的筛查工具
- 支持自定义风险因素和权重
- 支持多种数据输入格式
- 预留国际化接口

### 4.3 可靠性

- 参数验证和异常处理
- 模拟结果的一致性检查
- 自动备份和恢复机制
- 详细的日志记录

### 4.4 易用性

- 直观的用户界面
- 详细的用户文档
- 示例配置和教程
- 错误提示和帮助信息

## 5. 项目里程碑

### 阶段1：基础架构（2周）

- 完善项目结构和模块划分
- 实现核心数据结构
- 建立基础的模拟框架

### 阶段2：核心模块（4周）

- 完善疾病自然史模块
- 实现筛查模块
- 完善人口和风险模块

### 阶段3：高级功能（3周）

- 实现机器学习校准模块
- 完善卫生经济学模块
- 实现数据管理功能

### 阶段4：用户界面（2周）

- 开发命令行界面
- 实现结果可视化
- 创建配置管理界面

### 阶段5：测试和文档（2周）

- 编写单元测试和集成测试
- 完善用户文档
- 性能优化和bug修复

## 6. 风险评估

### 6.1 技术风险

- 机器学习校准算法的收敛性
- 大规模人群模拟的性能瓶颈
- 复杂疾病进展模型的准确性

### 6.2 数据风险

- 中国本土化参数数据的获取
- 文献数据的质量和一致性
- 生命表数据的时效性

### 6.3 缓解措施

- 采用成熟的机器学习框架
- 实现分布式计算支持
- 建立参数验证和校准机制
- 提供多种数据源接口

## 7. 开发计划

### 7.1 开发环境

- Python 3.8+ 开发环境
- Git 版本控制
- IDE: PyCharm/VSCode
- 虚拟环境管理

### 7.2 质量保证

- 代码审查机制
- 自动化测试
- 持续集成/持续部署
- 性能监控

### 7.3 交付物

- 源代码及文档
- 用户手册
- 技术文档
- 测试报告
- 部署指南
