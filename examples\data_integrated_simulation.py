"""
数据集成模拟示例
演示如何使用集成了实际数据的CCSM模型进行筛查策略模拟
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.ccsm.core.model import ColorectalCancerMicrosimulationModel
from src.ccsm.modules.screening import ScreeningStrategy, ScreeningToolConfig
from src.ccsm.core.enums import ScreeningTool


def main():
    """主函数"""
    print("=== 基于实际数据的结直肠癌筛查微观模拟模型示例 ===\n")

    # 1. 创建模型实例
    print("1. 创建模型实例...")
    model = ColorectalCancerMicrosimulationModel(initial_population=1000)
    print("   ✅ 模型创建完成，已自动加载实际数据\n")

    # 2. 设置人口分布（使用实际数据）
    print("2. 设置人口分布...")
    print("   使用南山区实际人口构成数据和中国2020寿命表数据")
    
    # 不传入参数，将自动使用实际数据
    model.setup_population()
    print("   ✅ 人口初始化完成\n")

    # 3. 模型校准
    print("\n3. 模型校准...")
    try:
        print("   使用基准数据进行模型校准...")
        calibration_results = model.calibrate_model(
            use_neural_network=True,  # 使用DNN校准方法
            n_samples=1000  # 减少样本数以加快速度
        )
        
        print("   ✅ 校准完成")
        print(f"   📊 校准结果:")
        print(f"      - 最佳参数组合: {len(calibration_results['best_parameters'])} 个参数")
        print(f"      - 校准损失: {calibration_results['best_loss']:.6f}")
        
        # 显示部分最佳参数
        best_params = calibration_results['best_parameters']
        param_names = list(best_params.keys())[:9]  # 显示前9个参数
        for param_name in param_names:
            print(f"      - {param_name}: {best_params[param_name]:.6f}")
        
    except Exception as e:
        print(f"   ⚠️ 校准过程出现问题: {e}")
        print("   继续使用默认参数...")

    # 4. 定义筛查策略
    print("4. 定义筛查策略...")
    
    # 策略1: 年度FIT筛查
    annual_fit = ScreeningStrategy(
        name="annual_fit",
        tool_configs=[
            ScreeningToolConfig(
                tool=ScreeningTool.FIT,
                start_age=50,
                end_age=75,
                interval=1.0,
                compliance_rate=0.70,
                follow_up_compliance_rate=0.75
            )
        ],
        sequential=False
    )
    model.screening_module.add_strategy(annual_fit)

    # 策略2: 双年度FIT筛查
    biennial_fit = ScreeningStrategy(
        name="biennial_fit",
        tool_configs=[
            ScreeningToolConfig(
                tool=ScreeningTool.FIT,
                start_age=50,
                end_age=75,
                interval=2.0,
                compliance_rate=0.70,
                follow_up_compliance_rate=0.75
            )
        ],
        sequential=False
    )
    model.screening_module.add_strategy(biennial_fit)
    
    # # 策略3: 10年结肠镜筛查
    # colonoscopy_10y = ScreeningStrategy(
    #     name="colonoscopy_10y",
    #     start_age=50,
    #     end_age=75,
    #     tools=[ScreeningTool.COLONOSCOPY],
    #     intervals=[10]
    # )
    # model.screening_module.add_strategy(colonoscopy_10y)
    
    print("   ✅ 筛查策略定义完成\n")

    # 5. 运行单一策略模拟
    print("5. 运行单一策略模拟...")
    print("   策略: 年度FIT筛查")
    
    results = model.run_simulation(years=10, screening_strategy="annual_fit")
    
    print("   ✅ 模拟完成")
    print(f"   📊 模拟结果:")

    # 检查结果字典结构并安全访问
    try:
        if 'health_outcome' in results:
            health_outcome = results['health_outcome']
            print(f"      - 检出癌症病例: {health_outcome.get('cancer_cases_detected', '数据不可用')}")
            print(f"      - 预防癌症病例: {health_outcome.get('cancer_cases_prevented', '数据不可用')}")
        else:
            # 从模拟摘要中提取信息
            summary = results.get('simulation_summary', {})
            print(f"      - 检出癌症病例: {summary.get('cancer_cases_detected', '请查看上方模拟摘要')}")
            print(f"      - 预防癌症病例: {summary.get('cancer_cases_prevented', '请查看上方模拟摘要')}")

        if 'economics_outcome' in results:
            economics_outcome = results['economics_outcome']
            print(f"      - 总成本: ¥{economics_outcome.get('total_cost', 0):,.0f}")
            print(f"      - QALYs获得: {economics_outcome.get('qalys_gained', 0):.2f}")
            print(f"      - ICER: ¥{economics_outcome.get('icer', 0):,.0f}/QALY")
        else:
            print(f"      - 经济学结果: 请查看上方模拟摘要")

    except Exception as e:
        print(f"      - 结果解析错误: {e}")
        print(f"      - 可用的结果键: {list(results.keys())}")

    print()

    # 6. 比较多个策略
    print("6. 比较多个筛查策略...")
    
    strategies = ["annual_fit", "biennial_fit"]
    comparison = model.compare_strategies(strategies, years=10)
    
    print("   ✅ 策略比较完成")
    print("   📊 比较结果:")

    try:
        if 'results' in comparison:
            for strategy_name, results in comparison['results'].items():
                print(f"      {strategy_name}:")

                # 安全访问结果数据
                if 'health_outcome' in results:
                    health_outcome = results['health_outcome']
                    print(f"        - 检出癌症: {health_outcome.get('cancer_cases_detected', '数据不可用')}")
                    print(f"        - 预防癌症: {health_outcome.get('cancer_cases_prevented', '数据不可用')}")
                else:
                    print(f"        - 健康结果: 请查看详细报告")

                if 'economics_outcome' in results:
                    economics_outcome = results['economics_outcome']
                    print(f"        - 总成本: ¥{economics_outcome.get('total_cost', 0):,.0f}")
                    print(f"        - QALYs: {economics_outcome.get('qalys_gained', 0):.2f}")
                    print(f"        - ICER: ¥{economics_outcome.get('icer', 0):,.0f}/QALY")
                else:
                    print(f"        - 经济学结果: 请查看详细报告")
        else:
            print("      策略比较结果格式异常，请查看详细输出")

        print("\n   💡 推荐策略:")
        if 'recommendations' in comparison:
            for recommendation in comparison['recommendations']:
                print(f"      • {recommendation}")
        else:
            print("      • 推荐信息不可用，请查看详细比较结果")

    except Exception as e:
        print(f"      策略比较结果解析错误: {e}")
        print(f"      可用的比较结果键: {list(comparison.keys()) if isinstance(comparison, dict) else '非字典类型'}")


    # 7. 生成报告
    print("\n7. 生成分析报告...")
    try:
        # 使用第一个策略的结果生成报告
        if results:
            model.generate_report(results, "simulation_report_with_real_data.html")
            print("   ✅ 报告已生成: simulation_report_with_real_data.html")
        else:
            print("   ⚠️ 无可用结果生成报告")
    except Exception as e:
        print(f"   ⚠️ 报告生成失败: {e}")

    # 8. 数据来源说明
    print("\n8. 数据来源说明:")
    print("   📁 本次模拟使用的实际数据文件:")
    print("      • default_benchmarks.xlsx - 校准基准值数据")
    print("        包含发病率、死亡率、腺瘤患病率等指标")
    print("      • lifetable.xlsx - 中国人口寿命表")
    print("        提供按年龄和性别分组的自然死亡率")
    print("      • population.xlsx - 南山区人口构成")
    print("        提供实际的年龄分布和性别比例")
    
    print("\n   🔧 模型参数自动调整:")
    print("      • 疾病自然史参数根据实际发病率和死亡率调整")
    print("      • 人口分布使用南山区实际数据")
    print("      • 校准目标基于实际流行病学数据设定")

    print("\n=== 模拟完成 ===")
    print("🎉 基于实际数据的结直肠癌筛查模拟已成功完成！")
    print("\n💡 提示:")
    print("   • 模型已自动集成实际数据，无需手动配置")
    print("   • 可以通过修改Excel文件来更新基准数据")
    print("   • 校准过程会自动使用最新的基准值")
    print("   • 结果更贴近实际情况，可用于政策制定参考")


if __name__ == "__main__":
    main()
