"""
测试增强的筛查功能
验证新的筛查策略配置、统计功能和贯序筛查逻辑
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.ccsm.modules.screening import (
    ScreeningModule, ScreeningParameters, ScreeningStrategy, ScreeningToolConfig
)
from src.ccsm.core.individual import Individual
from src.ccsm.core.enums import Gender, ScreeningTool, CancerStage


def test_screening_tool_config():
    """测试筛查工具配置"""
    print("=== 测试筛查工具配置 ===")
    
    # 创建筛查工具配置
    fit_config = ScreeningToolConfig(
        tool=ScreeningTool.FIT,
        start_age=50,
        end_age=75,
        interval=1.0,
        compliance_rate=0.70,
        follow_up_compliance_rate=0.75
    )
    
    assert fit_config.tool == ScreeningTool.FIT
    assert fit_config.start_age == 50
    assert fit_config.end_age == 75
    assert fit_config.interval == 1.0
    assert fit_config.compliance_rate == 0.70
    assert fit_config.follow_up_compliance_rate == 0.75
    
    print("✅ 筛查工具配置测试通过")


def test_enhanced_screening_strategy():
    """测试增强的筛查策略"""
    print("\n=== 测试增强的筛查策略 ===")
    
    # 创建多阶段筛查策略
    strategy = ScreeningStrategy(
        name="test_multi_stage",
        tool_configs=[
            ScreeningToolConfig(
                tool=ScreeningTool.FIT,
                start_age=50,
                end_age=65,
                interval=2.0,
                compliance_rate=0.70,
                follow_up_compliance_rate=0.75
            ),
            ScreeningToolConfig(
                tool=ScreeningTool.COLONOSCOPY,
                start_age=65,
                end_age=75,
                interval=10.0,
                compliance_rate=0.60
            )
        ],
        sequential=True
    )
    
    assert strategy.name == "test_multi_stage"
    assert len(strategy.tool_configs) == 2
    assert strategy.tool_configs[0].tool == ScreeningTool.FIT
    assert strategy.tool_configs[1].tool == ScreeningTool.COLONOSCOPY
    assert strategy.sequential == True
    
    # 测试年龄范围验证
    assert strategy.tool_configs[0].start_age == 50
    assert strategy.tool_configs[0].end_age == 65
    assert strategy.tool_configs[1].start_age == 65
    assert strategy.tool_configs[1].end_age == 75
    
    print("✅ 增强的筛查策略测试通过")


def test_age_range_validation():
    """测试年龄范围验证"""
    print("\n=== 测试年龄范围验证 ===")
    
    try:
        # 测试无效的年龄范围（开始年龄大于结束年龄）
        invalid_strategy = ScreeningStrategy(
            name="invalid_age_range",
            tool_configs=[
                ScreeningToolConfig(
                    tool=ScreeningTool.FIT,
                    start_age=75,  # 开始年龄大于结束年龄
                    end_age=50,
                    interval=1.0
                )
            ]
        )
        assert False, "应该抛出年龄范围错误"
    except ValueError as e:
        print(f"✅ 正确捕获年龄范围错误: {e}")
    
    # 测试贯序筛查中年龄范围重叠是允许的（因为这在实际应用中是合理的）
    valid_sequential = ScreeningStrategy(
        name="valid_sequential",
        tool_configs=[
            ScreeningToolConfig(
                tool=ScreeningTool.FIT,
                start_age=50,
                end_age=70,
                interval=1.0
            ),
            ScreeningToolConfig(
                tool=ScreeningTool.COLONOSCOPY,
                start_age=60,  # 年龄范围重叠是允许的
                end_age=75,
                interval=10.0
            )
        ],
        sequential=True
    )
    print("✅ 贯序筛查中年龄范围重叠是允许的")


def test_backward_compatibility():
    """测试向后兼容性"""
    print("\n=== 测试向后兼容性 ===")
    
    # 使用旧格式创建策略
    old_format_strategy = ScreeningStrategy(
        name="old_format",
        start_age=50,
        end_age=75,
        tools=[ScreeningTool.FIT],
        intervals=[1.0],
        sequential=False
    )
    
    # 验证自动转换为新格式
    assert len(old_format_strategy.tool_configs) == 1
    assert old_format_strategy.tool_configs[0].tool == ScreeningTool.FIT
    assert old_format_strategy.tool_configs[0].start_age == 50
    assert old_format_strategy.tool_configs[0].end_age == 75
    assert old_format_strategy.tool_configs[0].interval == 1.0
    
    print("✅ 向后兼容性测试通过")


def test_enhanced_statistics():
    """测试增强的统计功能"""
    print("\n=== 测试增强的统计功能 ===")
    
    # 创建筛查模块
    screening_module = ScreeningModule()
    screening_module.create_predefined_strategies()
    
    # 创建测试个体
    individual = Individual(
        id=1,
        gender=Gender.MALE,
        birth_year=1970,
        current_age=54
    )
    individual.cancer_stage = CancerStage.HIGH_RISK_ADENOMA  # 设置有病变状态
    
    # 执行筛查
    records = screening_module.perform_screening(individual, "annual_fit", 2024)
    
    # 验证记录包含新的统计字段
    if records:
        record = records[0]
        assert hasattr(record, 'is_primary_screening')
        assert hasattr(record, 'is_diagnostic_colonoscopy')
        assert hasattr(record, 'triggered_by_tool')
        assert hasattr(record, 'strategy_name')
        assert record.strategy_name == "annual_fit"
        print("✅ 筛查记录包含新的统计字段")
    
    # 测试人群筛查统计
    population = [individual]
    stats = screening_module.screen_population(population, "annual_fit", 2024)
    
    # 验证新的统计字段
    assert 'screening_counts_by_tool' in stats
    assert 'diagnostic_colonoscopy_count' in stats
    assert 'primary_screening_count' in stats
    assert 'follow_up_compliance_rate' in stats
    
    print("✅ 增强的统计功能测试通过")


def test_sequential_screening_logic():
    """测试贯序筛查逻辑"""
    print("\n=== 测试贯序筛查逻辑 ===")
    
    # 创建筛查模块
    screening_module = ScreeningModule()
    screening_module.create_predefined_strategies()
    
    # 创建测试个体
    individual = Individual(
        id=1,
        gender=Gender.MALE,
        birth_year=1970,
        current_age=54
    )
    individual.cancer_stage = CancerStage.HIGH_RISK_ADENOMA  # 设置有病变状态
    
    # 执行FIT+结肠镜贯序筛查
    records = screening_module.perform_screening(individual, "fit_colonoscopy", 2024)
    
    # 验证贯序筛查逻辑
    print(f"筛查记录数量: {len(records)}")
    for i, record in enumerate(records):
        print(f"记录 {i+1}: {record.tool}, 检测到: {record.detected}, "
              f"初筛: {record.is_primary_screening}, 确诊性肠镜: {record.is_diagnostic_colonoscopy}")
    
    print("✅ 贯序筛查逻辑测试完成")


def run_all_tests():
    """运行所有测试"""
    print("开始运行增强筛查功能测试...\n")
    
    try:
        test_screening_tool_config()
        test_enhanced_screening_strategy()
        test_age_range_validation()
        test_backward_compatibility()
        test_enhanced_statistics()
        test_sequential_screening_logic()
        
        print("\n🎉 所有测试通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    run_all_tests()
