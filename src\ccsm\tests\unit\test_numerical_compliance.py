"""
测试数值型依从性功能
验证确诊性肠镜依从性的数值型处理
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.ccsm.modules.screening import (
    ScreeningModule, ScreeningParameters, ScreeningStrategy, ScreeningToolConfig
)
from src.ccsm.core.individual import Individual
from src.ccsm.core.enums import Gender, ScreeningTool, CancerStage
import random


def test_numerical_compliance_recording():
    """测试数值型依从性的记录"""
    print("=== 测试数值型依从性记录 ===")
    
    # 创建筛查策略，设置特定的依从性概率
    strategy = ScreeningStrategy(
        name="test_numerical_compliance",
        tool_configs=[
            ScreeningToolConfig(
                tool=ScreeningTool.FIT,
                start_age=50,
                end_age=75,
                interval=1.0,
                compliance_rate=1.0,  # 100%筛查依从性，确保执行筛查
                follow_up_compliance_rate=0.75  # 75%后续肠镜依从性
            )
        ],
        sequential=False
    )
    
    # 创建筛查模块
    screening_module = ScreeningModule()
    screening_module.add_strategy(strategy)
    
    # 创建有病变的个体（确保FIT阳性）
    individual = Individual(
        id=1,
        gender=Gender.MALE,
        birth_year=1970,
        current_age=54
    )
    individual.cancer_stage = CancerStage.HIGH_RISK_ADENOMA
    
    # 执行多次筛查来验证概率
    compliance_rates = []
    detected_count = 0
    
    for i in range(100):  # 执行100次测试
        # 重置个体状态
        test_individual = Individual(
            id=i,
            gender=Gender.MALE,
            birth_year=1970,
            current_age=54
        )
        test_individual.cancer_stage = CancerStage.HIGH_RISK_ADENOMA
        
        # 执行筛查
        records = screening_module.perform_screening(test_individual, "test_numerical_compliance", 2024)
        
        if records:
            record = records[0]
            if record.detected:
                detected_count += 1
                # 验证记录包含数值型依从性
                assert hasattr(record, 'follow_up_compliance_rate'), "筛查记录应包含follow_up_compliance_rate字段"
                assert record.follow_up_compliance_rate == 0.75, f"依从性概率应为0.75，实际为{record.follow_up_compliance_rate}"
                compliance_rates.append(record.follow_up_compliance_rate)
    
    print(f"检测到阳性的次数: {detected_count}")
    print(f"记录的依从性概率: {compliance_rates[0] if compliance_rates else 'N/A'}")
    print("✅ 数值型依从性记录测试通过")


def test_sequential_screening_with_numerical_compliance():
    """测试贯序筛查中的数值型依从性"""
    print("\n=== 测试贯序筛查中的数值型依从性 ===")
    
    # 设置随机种子以获得可重现的结果
    random.seed(42)
    
    # 创建FIT+肠镜贯序筛查策略
    strategy = ScreeningStrategy(
        name="fit_colonoscopy_numerical",
        tool_configs=[
            ScreeningToolConfig(
                tool=ScreeningTool.FIT,
                start_age=50,
                end_age=75,
                interval=1.0,
                compliance_rate=1.0,
                follow_up_compliance_rate=0.8  # 80%后续肠镜依从性
            )
        ],
        sequential=True
    )
    
    # 创建筛查模块
    screening_module = ScreeningModule()
    screening_module.add_strategy(strategy)
    
    # 创建有病变的个体
    individual = Individual(
        id=1,
        gender=Gender.MALE,
        birth_year=1970,
        current_age=54
    )
    individual.cancer_stage = CancerStage.HIGH_RISK_ADENOMA
    
    # 执行贯序筛查
    records = screening_module.perform_screening(individual, "fit_colonoscopy_numerical", 2024)
    
    print(f"筛查记录数量: {len(records)}")
    
    fit_record = None
    colonoscopy_record = None
    
    for record in records:
        print(f"工具: {record.tool}, 检测到: {record.detected}, 初筛: {record.is_primary_screening}, 确诊性肠镜: {record.is_diagnostic_colonoscopy}")
        if record.tool == "FIT":
            fit_record = record
        elif record.tool == "Colonoscopy":
            colonoscopy_record = record
    
    # 验证FIT记录
    if fit_record:
        assert hasattr(fit_record, 'follow_up_compliance_rate'), "FIT记录应包含follow_up_compliance_rate"
        assert fit_record.follow_up_compliance_rate == 0.8, f"FIT后续依从性应为0.8，实际为{fit_record.follow_up_compliance_rate}"
        print(f"FIT后续依从性概率: {fit_record.follow_up_compliance_rate}")
    
    # 验证是否触发了确诊性肠镜
    if colonoscopy_record:
        assert colonoscopy_record.is_diagnostic_colonoscopy, "应该是确诊性肠镜"
        assert colonoscopy_record.triggered_by_tool == "FIT", "应该由FIT触发"
        print("✅ 确诊性肠镜正确触发")
    
    print("✅ 贯序筛查中的数值型依从性测试通过")


def test_population_compliance_statistics():
    """测试人群依从性统计"""
    print("\n=== 测试人群依从性统计 ===")
    
    # 创建筛查模块
    screening_module = ScreeningModule()
    screening_module.create_predefined_strategies()
    
    # 创建测试人群
    population = []
    for i in range(50):
        individual = Individual(
            id=i,
            gender=Gender.MALE if i % 2 == 0 else Gender.FEMALE,
            birth_year=1970,
            current_age=54
        )
        # 设置一些个体有病变
        if i % 5 == 0:
            individual.cancer_stage = CancerStage.HIGH_RISK_ADENOMA
        population.append(individual)
    
    # 执行人群筛查
    stats = screening_module.screen_population(population, "annual_fit", 2024)
    
    print(f"符合条件人群: {stats['eligible_population']}")
    print(f"筛查人群: {stats['screened_population']}")
    print(f"检出病例: {stats['detected_cases']}")
    print(f"后续依从率: {stats['follow_up_compliance_rate']*100:.1f}%")
    
    # 验证新增的平均依从性概率统计
    if 'average_follow_up_compliance_rate' in stats:
        print(f"平均后续依从性概率: {stats['average_follow_up_compliance_rate']*100:.1f}%")
        print("✅ 包含平均依从性概率统计")
    
    print("✅ 人群依从性统计测试通过")


def test_compliance_rate_consistency():
    """测试依从性概率的一致性"""
    print("\n=== 测试依从性概率一致性 ===")
    
    # 创建具有不同依从性的策略
    high_compliance_strategy = ScreeningStrategy(
        name="high_compliance",
        tool_configs=[
            ScreeningToolConfig(
                tool=ScreeningTool.FIT,
                start_age=50,
                end_age=75,
                interval=1.0,
                compliance_rate=1.0,
                follow_up_compliance_rate=0.9  # 90%依从性
            )
        ],
        sequential=False
    )
    
    low_compliance_strategy = ScreeningStrategy(
        name="low_compliance",
        tool_configs=[
            ScreeningToolConfig(
                tool=ScreeningTool.FIT,
                start_age=50,
                end_age=75,
                interval=1.0,
                compliance_rate=1.0,
                follow_up_compliance_rate=0.3  # 30%依从性
            )
        ],
        sequential=False
    )
    
    # 创建筛查模块
    screening_module = ScreeningModule()
    screening_module.add_strategy(high_compliance_strategy)
    screening_module.add_strategy(low_compliance_strategy)
    
    # 创建测试人群
    population = []
    for i in range(100):
        individual = Individual(
            id=i,
            gender=Gender.MALE,
            birth_year=1970,
            current_age=54
        )
        individual.cancer_stage = CancerStage.HIGH_RISK_ADENOMA  # 确保检出
        population.append(individual)
    
    # 测试高依从性策略
    high_stats = screening_module.screen_population(population.copy(), "high_compliance", 2024)
    
    # 重置人群状态
    for individual in population:
        individual.screening_history.clear()
        individual.last_screening_year = None
    
    # 测试低依从性策略
    low_stats = screening_module.screen_population(population.copy(), "low_compliance", 2024)
    
    print(f"高依从性策略平均依从性概率: {high_stats.get('average_follow_up_compliance_rate', 0)*100:.1f}%")
    print(f"低依从性策略平均依从性概率: {low_stats.get('average_follow_up_compliance_rate', 0)*100:.1f}%")
    
    # 验证依从性概率的差异
    if 'average_follow_up_compliance_rate' in high_stats and 'average_follow_up_compliance_rate' in low_stats:
        assert high_stats['average_follow_up_compliance_rate'] > low_stats['average_follow_up_compliance_rate'], \
            "高依从性策略的平均依从性概率应该更高"
        print("✅ 依从性概率差异验证通过")
    
    print("✅ 依从性概率一致性测试通过")


def run_all_tests():
    """运行所有测试"""
    print("开始运行数值型依从性测试...\n")
    
    try:
        test_numerical_compliance_recording()
        test_sequential_screening_with_numerical_compliance()
        test_population_compliance_statistics()
        test_compliance_rate_consistency()
        
        print("\n🎉 所有数值型依从性测试通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    run_all_tests()
