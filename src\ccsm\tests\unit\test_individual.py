"""
个体数据结构测试
"""

import unittest
from datetime import datetime

from ..core.individual import Individual, Adenoma, ScreeningRecord, TreatmentRecord
from ..core.enums import Gender, CancerStage, AdenomaLocation, DeathCause, RiskFactor


class TestAdenoma(unittest.TestCase):
    """腺瘤测试类"""

    def setUp(self):
        """测试前准备"""
        self.adenoma = Adenoma(
            id=1,
            location=AdenomaLocation.DISTAL_COLON,
            size=8.0,
            has_villous=False,
            has_high_grade_dysplasia=False,
            onset_age=55,
            onset_year=2020
        )

    def test_adenoma_creation(self):
        """测试腺瘤创建"""
        self.assertEqual(self.adenoma.id, 1)
        self.assertEqual(self.adenoma.location, AdenomaLocation.DISTAL_COLON)
        self.assertEqual(self.adenoma.size, 8.0)
        self.assertFalse(self.adenoma.has_villous)
        self.assertFalse(self.adenoma.has_high_grade_dysplasia)

    def test_is_high_risk_size(self):
        """测试基于大小的高风险判断"""
        # 低风险（小于10mm）
        self.assertFalse(self.adenoma.is_high_risk)

        # 高风险（大于等于10mm）
        self.adenoma.size = 12.0
        self.assertTrue(self.adenoma.is_high_risk)

    def test_is_high_risk_villous(self):
        """测试基于绒毛的高风险判断"""
        self.adenoma.has_villous = True
        self.assertTrue(self.adenoma.is_high_risk)

    def test_is_high_risk_dysplasia(self):
        """测试基于上皮内瘤变的高风险判断"""
        self.adenoma.has_high_grade_dysplasia = True
        self.assertTrue(self.adenoma.is_high_risk)


class TestIndividual(unittest.TestCase):
    """个体测试类"""

    def setUp(self):
        """测试前准备"""
        self.individual = Individual(
            id=1,
            gender=Gender.MALE,
            birth_year=1970,
            current_age=50
        )

    def test_individual_creation(self):
        """测试个体创建"""
        self.assertEqual(self.individual.id, 1)
        self.assertEqual(self.individual.gender, Gender.MALE)
        self.assertEqual(self.individual.birth_year, 1970)
        self.assertEqual(self.individual.current_age, 50)
        self.assertTrue(self.individual.alive)
        self.assertEqual(self.individual.cancer_stage, CancerStage.NORMAL)
        self.assertEqual(self.individual.death_cause, DeathCause.ALIVE)

    def test_risk_factors_initialization(self):
        """测试风险因素初始化"""
        # 检查风险因素字典是否正确初始化
        self.assertIsInstance(self.individual.risk_factors, dict)
        for factor in RiskFactor:
            self.assertIn(factor, self.individual.risk_factors)
            self.assertFalse(self.individual.risk_factors[factor])

    def test_get_age_at_year(self):
        """测试年龄计算"""
        age_2020 = self.individual.get_age_at_year(2020)
        self.assertEqual(age_2020, 50)

        age_2025 = self.individual.get_age_at_year(2025)
        self.assertEqual(age_2025, 55)

    def test_add_adenoma(self):
        """测试添加腺瘤"""
        adenoma = Adenoma(
            id=1,
            location=AdenomaLocation.PROXIMAL_COLON,
            size=6.0,
            onset_age=52,
            onset_year=2022
        )

        self.individual.add_adenoma(adenoma)
        self.assertEqual(len(self.individual.adenomas), 1)
        self.assertEqual(self.individual.adenoma_count, 1)
        self.assertEqual(self.individual.adenomas[0], adenoma)

    def test_remove_adenoma(self):
        """测试移除腺瘤"""
        adenoma = Adenoma(
            id=1,
            location=AdenomaLocation.PROXIMAL_COLON,
            size=6.0,
            onset_age=52,
            onset_year=2022
        )

        self.individual.add_adenoma(adenoma)
        self.assertEqual(len(self.individual.adenomas), 1)

        # 移除存在的腺瘤
        result = self.individual.remove_adenoma(1)
        self.assertTrue(result)
        self.assertEqual(len(self.individual.adenomas), 0)
        self.assertEqual(self.individual.adenoma_count, 0)

        # 尝试移除不存在的腺瘤
        result = self.individual.remove_adenoma(999)
        self.assertFalse(result)

    def test_get_high_risk_adenomas(self):
        """测试获取高风险腺瘤"""
        # 添加低风险腺瘤
        low_risk_adenoma = Adenoma(
            id=1,
            location=AdenomaLocation.DISTAL_COLON,
            size=5.0,
            onset_age=52,
            onset_year=2022
        )

        # 添加高风险腺瘤
        high_risk_adenoma = Adenoma(
            id=2,
            location=AdenomaLocation.RECTUM,
            size=12.0,
            onset_age=53,
            onset_year=2023
        )

        self.individual.add_adenoma(low_risk_adenoma)
        self.individual.add_adenoma(high_risk_adenoma)

        high_risk_list = self.individual.get_high_risk_adenomas()
        self.assertEqual(len(high_risk_list), 1)
        self.assertEqual(high_risk_list[0], high_risk_adenoma)

        low_risk_list = self.individual.get_low_risk_adenomas()
        self.assertEqual(len(low_risk_list), 1)
        self.assertEqual(low_risk_list[0], low_risk_adenoma)

    def test_add_screening_record(self):
        """测试添加筛查记录"""
        record = ScreeningRecord(
            year=2020,
            age=50,
            tool="FIT",
            compliant=True,
            detected=False,
            cost=50.0
        )

        self.individual.add_screening_record(record)
        self.assertEqual(len(self.individual.screening_history), 1)
        self.assertEqual(self.individual.last_screening_year, 2020)
        self.assertEqual(self.individual.total_screening_cost, 50.0)

    def test_add_treatment_record(self):
        """测试添加治疗记录"""
        record = TreatmentRecord(
            year=2021,
            age=51,
            treatment_type="adenoma_removal",
            cancer_stage=CancerStage.LOW_RISK_ADENOMA,
            cost=2000.0
        )

        self.individual.add_treatment_record(record)
        self.assertEqual(len(self.individual.treatment_history), 1)
        self.assertEqual(self.individual.total_treatment_cost, 2000.0)

    def test_set_death(self):
        """测试设置死亡状态"""
        self.individual.set_death(2025, DeathCause.CANCER)

        self.assertFalse(self.individual.alive)
        self.assertEqual(self.individual.death_year, 2025)
        self.assertEqual(self.individual.death_cause, DeathCause.CANCER)

    def test_is_eligible_for_screening(self):
        """测试筛查资格判断"""
        # 50岁，符合筛查条件
        eligible = self.individual.is_eligible_for_screening(2020, 50, 75)
        self.assertTrue(eligible)

        # 49岁，不符合筛查条件
        eligible = self.individual.is_eligible_for_screening(2019, 50, 75)
        self.assertFalse(eligible)

        # 76岁，不符合筛查条件
        eligible = self.individual.is_eligible_for_screening(2046, 50, 75)
        self.assertFalse(eligible)

        # 死亡个体，不符合筛查条件
        self.individual.set_death(2025, DeathCause.NATURAL)
        eligible = self.individual.is_eligible_for_screening(2026, 50, 75)
        self.assertFalse(eligible)

    def test_get_screening_interval_since_last(self):
        """测试筛查间隔计算"""
        # 没有筛查历史
        interval = self.individual.get_screening_interval_since_last(2020)
        self.assertEqual(interval, float('inf'))

        # 添加筛查记录
        record = ScreeningRecord(
            year=2018,
            age=48,
            tool="FIT",
            compliant=True,
            detected=False,
            cost=50.0
        )
        self.individual.add_screening_record(record)

        interval = self.individual.get_screening_interval_since_last(2020)
        self.assertEqual(interval, 2)

    def test_to_dict(self):
        """测试转换为字典"""
        data = self.individual.to_dict()

        self.assertIsInstance(data, dict)
        self.assertEqual(data['id'], 1)
        self.assertEqual(data['gender'], Gender.MALE.value)
        self.assertEqual(data['birth_year'], 1970)
        self.assertEqual(data['current_age'], 50)
        self.assertTrue(data['alive'])
        self.assertEqual(data['cancer_stage'], CancerStage.NORMAL.value)


if __name__ == '__main__':
    unittest.main()
