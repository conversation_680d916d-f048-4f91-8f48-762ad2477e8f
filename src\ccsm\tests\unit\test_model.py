"""
主模型测试
"""

import unittest
from unittest.mock import patch, MagicMock

from ..core.model import ColorectalCancerMicrosimulationModel, ModelConfiguration
from ..core.enums import Gender, ScreeningTool
from ..modules.screening import ScreeningStrategy


class TestColorectalCancerMicrosimulationModel(unittest.TestCase):
    """主模型测试类"""

    def setUp(self):
        """测试前准备"""
        self.model = ColorectalCancerMicrosimulationModel(initial_population=100)

    def test_model_initialization(self):
        """测试模型初始化"""
        self.assertIsNotNone(self.model.population_module)
        self.assertIsNotNone(self.model.disease_module)
        self.assertIsNotNone(self.model.screening_module)
        self.assertIsNotNone(self.model.economics_module)
        self.assertIsNotNone(self.model.calibration_module)
        self.assertFalse(self.model.is_initialized)
        self.assertEqual(self.model.config.initial_population, 100)

    def test_setup_population(self):
        """测试人口设置"""
        age_distribution = {50: 0.2, 55: 0.3, 60: 0.3, 65: 0.2}
        gender_ratio = 0.5

        self.model.setup_population(age_distribution, gender_ratio)

        self.assertTrue(self.model.is_initialized)
        self.assertEqual(len(self.model.population_module.population), 100)

        # 检查年龄分布
        ages = [ind.current_age for ind in self.model.population_module.population]
        self.assertTrue(all(age in [50, 55, 60, 65] for age in ages))

        # 检查性别分布
        genders = [ind.gender for ind in self.model.population_module.population]
        male_count = sum(1 for g in genders if g == Gender.MALE)
        female_count = sum(1 for g in genders if g == Gender.FEMALE)

        # 允许一定的随机误差
        self.assertAlmostEqual(male_count / 100, 0.5, delta=0.2)
        self.assertAlmostEqual(female_count / 100, 0.5, delta=0.2)

    def test_add_screening_strategy(self):
        """测试添加筛查策略"""
        strategy = ScreeningStrategy(
            name="test_strategy",
            start_age=50,
            end_age=75,
            tools=[ScreeningTool.FIT],
            intervals=[1]
        )

        self.model.add_screening_strategy(strategy)

        self.assertIn("test_strategy", self.model.screening_module.strategies)
        retrieved_strategy = self.model.screening_module.get_strategy("test_strategy")
        self.assertEqual(retrieved_strategy.name, "test_strategy")

    def test_run_simulation_not_initialized(self):
        """测试未初始化时运行模拟"""
        with self.assertRaises(ValueError):
            self.model.run_simulation(5, "annual_fit")

    def test_run_simulation_invalid_strategy(self):
        """测试使用无效策略运行模拟"""
        age_distribution = {50: 1.0}
        self.model.setup_population(age_distribution)

        with self.assertRaises(ValueError):
            self.model.run_simulation(5, "invalid_strategy")

    @patch('builtins.print')  # 抑制打印输出
    def test_run_simulation_basic(self, mock_print):
        """测试基本模拟运行"""
        # 设置小规模人口以加快测试
        age_distribution = {50: 0.5, 60: 0.5}
        self.model.setup_population(age_distribution, 0.5)

        # 运行短期模拟
        results = self.model.run_simulation(2, "annual_fit", show_progress=False)

        # 验证结果结构
        self.assertIn('model_config', results)
        self.assertIn('yearly_results', results)
        self.assertIn('final_population_stats', results)
        self.assertIn('final_disease_stats', results)
        self.assertIn('screening_summary', results)
        self.assertIn('economics_outcome', results)

        # 验证配置信息
        config = results['model_config']
        self.assertEqual(config['initial_population'], 100)
        self.assertEqual(config['simulation_years'], 2)
        self.assertEqual(config['screening_strategy'], 'annual_fit')

        # 验证年度结果
        yearly_results = results['yearly_results']
        self.assertEqual(len(yearly_results), 2)

        for year_result in yearly_results:
            self.assertIn('year', year_result)
            self.assertIn('population_stats', year_result)
            self.assertIn('disease_stats', year_result)
            self.assertIn('screening_stats', year_result)

    @patch('builtins.print')
    def test_compare_strategies(self, mock_print):
        """测试策略比较"""
        age_distribution = {50: 1.0}
        self.model.setup_population(age_distribution, 0.5)

        strategies = ["annual_fit", "biennial_fit"]
        comparison_results = self.model.compare_strategies(
            strategies, 2, show_progress=False
        )

        # 验证比较结果结构
        self.assertIn('comparison_summary', comparison_results)
        self.assertIn('strategy_results', comparison_results)

        # 验证策略结果
        strategy_results = comparison_results['strategy_results']
        self.assertEqual(len(strategy_results), 2)
        self.assertIn('annual_fit', strategy_results)
        self.assertIn('biennial_fit', strategy_results)

    def test_get_model_summary(self):
        """测试获取模型摘要"""
        summary = self.model.get_model_summary()

        self.assertIn('model_info', summary)
        self.assertIn('configuration', summary)
        self.assertIn('available_strategies', summary)
        self.assertIn('completed_simulations', summary)

        model_info = summary['model_info']
        self.assertEqual(model_info['version'], '1.0.0')
        self.assertFalse(model_info['initialized'])

        config = summary['configuration']
        self.assertEqual(config['initial_population'], 100)


class TestModelConfiguration(unittest.TestCase):
    """模型配置测试类"""

    def test_default_configuration(self):
        """测试默认配置"""
        config = ModelConfiguration()

        self.assertEqual(config.initial_population, 10000)
        self.assertEqual(config.simulation_years, 20)
        self.assertEqual(config.start_year, 2020)
        self.assertIsNone(config.random_seed)

    def test_custom_configuration(self):
        """测试自定义配置"""
        config = ModelConfiguration(
            initial_population=5000,
            simulation_years=10,
            start_year=2025,
            random_seed=42
        )

        self.assertEqual(config.initial_population, 5000)
        self.assertEqual(config.simulation_years, 10)
        self.assertEqual(config.start_year, 2025)
        self.assertEqual(config.random_seed, 42)


if __name__ == '__main__':
    unittest.main()
